<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class NavigationMenu extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'label',
        'icon',
        'url',
        'route',
        'plugin',
        'permissions',
        'parent_id',
        'sort_order',
        'is_active',
        'visible',
        'is_system',
        'target',
        'metadata',
    ];

    protected $casts = [
        'permissions' => 'array',
        'metadata' => 'array',
        'is_active' => 'boolean',
        'is_system' => 'boolean',
    ];

    /**
     * Get the parent menu item
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(NavigationMenu::class, 'parent_id');
    }

    /**
     * Get the child menu items
     */
    public function children(): HasMany
    {
        return $this->hasMany(NavigationMenu::class, 'parent_id')->orderBy('sort_order');
    }

    /**
     * Get active child menu items
     */
    public function activeChildren(): HasMany
    {
        return $this->children()->where('is_active', true);
    }

    /**
     * Scope to get only active menu items
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get only root menu items (no parent)
     */
    public function scopeRoot($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope to get menu items ordered by sort_order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Check if user has permission to see this menu item
     */
    public function userHasPermission($user): bool
    {
        // If no permissions are required, allow access
        if (!$this->permissions || empty($this->permissions)) {
            return true;
        }

        // If no user is logged in, only show items with no permissions
        if (!$user) {
            return false;
        }

        // If user has no role, only show items with no permissions
        if (!$user->role) {
            return false;
        }

        // If user is admin or has manage_plugins permission, allow access to everything
        if ($user->hasPermission('manage_plugins')) {
            return true;
        }

        // Check if user has any of the required permissions
        foreach ($this->permissions as $permission) {
            if ($user->hasPermission($permission)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get the URL for this menu item
     */
    public function getUrlAttribute($value): ?string
    {
        if ($value) {
            return $value;
        }

        if ($this->route) {
            try {
                return route($this->route);
            } catch (\Exception $e) {
                return null;
            }
        }

        return null;
    }

    /**
     * Check if this menu item is currently active
     */
    public function isActive(): bool
    {
        if (!$this->url) {
            return false;
        }

        $currentUrl = request()->url();
        $currentPath = parse_url($currentUrl, PHP_URL_PATH);
        $menuPath = parse_url($this->url, PHP_URL_PATH);

        return $currentPath === $menuPath || str_starts_with($currentPath, $menuPath . '/');
    }

    /**
     * Get the full navigation tree
     */
    public static function getNavigationTree($user = null): array
    {
        $rootItems = self::whereNull('parent_id')
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        $navigationTree = [];

        foreach ($rootItems as $item) {
            // Simple permission check - if no permissions required, allow access
            if (empty($item->permissions) || ($user && $user->hasPermission('manage_plugins'))) {
                $itemArray = self::formatNavigationItem($item);

                // Load children
                $children = self::where('parent_id', $item->id)
                    ->where('is_active', true)
                    ->orderBy('sort_order')
                    ->get();

                $filteredChildren = [];
                foreach ($children as $child) {
                    if (empty($child->permissions) || ($user && $user->hasPermission('manage_plugins'))) {
                        $filteredChildren[] = self::formatNavigationItem($child);
                    }
                }

                $itemArray['filtered_children'] = $filteredChildren;
                $navigationTree[] = $itemArray;
            }
        }

        return $navigationTree;
    }

    /**
     * Format navigation item for consistent array structure
     */
    private static function formatNavigationItem($item): array
    {
        return [
            'id' => $item->id,
            'name' => $item->name,
            'type' => $item->type ?? 'link',
            'label' => $item->label,
            'icon' => $item->icon,
            'url' => $item->url,
            'route' => $item->route,
            'plugin' => $item->plugin,
            'parent_id' => $item->parent_id,
            'sort_order' => $item->sort_order,
            'is_active' => $item->is_active,
            'visible' => $item->visible,
            'permissions' => $item->permissions,
            'target' => $item->target ?? '_self',
            'metadata' => $item->metadata
        ];
    }

    /**
     * Test method to debug navigation tree
     */
    public static function testNavigationTree($user = null): array
    {
        return ['test' => 'working', 'user' => $user ? $user->email : 'none'];
    }

    /**
     * Get optimized navigation tree with minimal database queries
     */
    public static function getOptimizedNavigationTree($user = null, $enabledPlugins = []): array
    {
        // Pre-load user permissions if user exists to avoid repeated queries
        $userPermissions = [];
        if ($user) {
            $userPermissions = $user->getCachedPermissions();
        }

        // Single query to get all navigation items with eager loading
        $allItems = self::where('is_active', true)
            ->orderBy('sort_order')
            ->get()
            ->groupBy('parent_id');

        $rootItems = $allItems->get(null, collect());
        $navigationTree = [];

        foreach ($rootItems as $item) {
            $hasPermission = $item->userHasPermission($user);
            $isVisible = $item->visible ?? true;
            $pluginEnabled = empty($item->plugin) || in_array($item->plugin, $enabledPlugins);

            if ($hasPermission && $isVisible && $pluginEnabled) {
                $itemArray = $item->toArray();

                // Get children from the grouped collection (no additional query)
                $children = $allItems->get($item->id, collect());
                $filteredChildren = [];

                foreach ($children as $child) {
                    $childHasPermission = $child->userHasPermission($user);
                    $childIsVisible = $child->visible ?? true;
                    $childPluginEnabled = empty($child->plugin) || in_array($child->plugin, $enabledPlugins);

                    if ($childHasPermission && $childIsVisible && $childPluginEnabled) {
                        $filteredChildren[] = $child->toArray();
                    }
                }

                $itemArray['filtered_children'] = $filteredChildren;
                $navigationTree[] = $itemArray;
            }
        }

        return $navigationTree;
    }



    /**
     * Get the next sort order for a given parent
     */
    public static function getNextSortOrder($parentId = null): int
    {
        return self::where('parent_id', $parentId)->max('sort_order') + 1;
    }

    /**
     * Clear navigation cache for all users
     */
    public static function clearNavigationCache(): void
    {
        // Clear navigation cache for all users
        $users = \App\Models\User::all();
        foreach ($users as $user) {
            cache()->forget('navigation_tree_' . $user->id);
        }

        // Clear guest navigation cache
        cache()->forget('navigation_tree_guest');

        // Clear enabled plugins cache
        cache()->forget('enabled_plugins');
    }

    /**
     * Boot method to clear cache when navigation changes
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function () {
            self::clearNavigationCache();
        });

        static::deleted(function () {
            self::clearNavigationCache();
        });
    }

    /**
     * Get the menu item type for display purposes
     */
    public function getItemType(): string
    {
        if ($this->type === 'separator') {
            return 'separator';
        }

        if ($this->is_system) {
            return 'system';
        }

        if ($this->plugin) {
            return 'plugin';
        }

        if ($this->route) {
            return 'internal';
        }

        if ($this->url && $this->target === '_blank') {
            return 'external';
        }

        if ($this->children()->count() > 0) {
            return 'parent';
        }

        return 'custom';
    }

    /**
     * Get color classes based on item type
     */
    public function getTypeColorClasses(): array
    {
        $type = $this->getItemType();

        return match($type) {
            'system' => [
                'bg' => 'bg-blue-50 dark:bg-blue-900/20',
                'border' => 'border-blue-200 dark:border-blue-800',
                'badge' => 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200',
                'icon' => 'text-blue-600 dark:text-blue-400'
            ],
            'plugin' => [
                'bg' => 'bg-purple-50 dark:bg-purple-900/20',
                'border' => 'border-purple-200 dark:border-purple-800',
                'badge' => 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200',
                'icon' => 'text-purple-600 dark:text-purple-400'
            ],
            'separator' => [
                'bg' => 'bg-gray-50 dark:bg-gray-800',
                'border' => 'border-gray-200 dark:border-gray-700',
                'badge' => 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200',
                'icon' => 'text-gray-400 dark:text-gray-500'
            ],
            'external' => [
                'bg' => 'bg-green-50 dark:bg-green-900/20',
                'border' => 'border-green-200 dark:border-green-800',
                'badge' => 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',
                'icon' => 'text-green-600 dark:text-green-400'
            ],
            'parent' => [
                'bg' => 'bg-orange-50 dark:bg-orange-900/20',
                'border' => 'border-orange-200 dark:border-orange-800',
                'badge' => 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200',
                'icon' => 'text-orange-600 dark:text-orange-400'
            ],
            'internal' => [
                'bg' => 'bg-indigo-50 dark:bg-indigo-900/20',
                'border' => 'border-indigo-200 dark:border-indigo-800',
                'badge' => 'bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200',
                'icon' => 'text-indigo-600 dark:text-indigo-400'
            ],
            default => [
                'bg' => 'bg-gray-50 dark:bg-gray-800',
                'border' => 'border-gray-200 dark:border-gray-700',
                'badge' => 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200',
                'icon' => 'text-gray-600 dark:text-gray-400'
            ]
        };
    }

    /**
     * Get human-readable type label
     */
    public function getTypeLabel(): string
    {
        $type = $this->getItemType();

        return match($type) {
            'system' => 'System',
            'plugin' => 'Plugin',
            'separator' => 'Separator',
            'external' => 'External Link',
            'parent' => 'Parent Menu',
            'internal' => 'Internal Link',
            default => 'Custom'
        };
    }

    /**
     * Get detailed information about the menu item
     */
    public function getDetailedInfo(): array
    {
        $info = [
            'type' => $this->getTypeLabel(),
            'destination' => 'None',
            'permissions' => $this->permissions ? count($this->permissions) : 0,
            'children' => $this->children()->count(),
            'status' => $this->is_active ? 'Active' : 'Inactive',
            'visibility' => $this->visible ? 'Visible' : 'Hidden'
        ];

        // Set destination based on type
        if ($this->route) {
            $info['destination'] = "Route: {$this->route}";
        } elseif ($this->url) {
            $info['destination'] = "URL: {$this->url}";
        } elseif ($this->type === 'separator') {
            $info['destination'] = 'Visual separator';
        } elseif ($this->children()->count() > 0) {
            $info['destination'] = 'Parent container';
        }

        return $info;
    }
}
